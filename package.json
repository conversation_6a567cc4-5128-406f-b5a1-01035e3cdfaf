{"name": "react-tailwindcss-portfolio", "version": "0.1.0", "private": true, "dependencies": {"@gsap/react": "^2.1.2", "@heroicons/react": "^2.1.1", "@react-three/drei": "^9.93.0", "@react-three/fiber": "^8.15.13", "@sendgrid/mail": "^8.1.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "cloudinary": "^2.5.1", "clsx": "^2.1.0", "concurrently": "^9.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "embla-carousel-autoplay": "^8.4.0", "embla-carousel-react": "^8.4.0", "express": "^4.21.1", "express-fileupload": "^1.5.1", "framer-motion": "^10.17.12", "gsap": "^3.12.7", "imagesloaded": "^5.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.460.0", "maath": "^0.10.8", "mongoose": "^8.15.0", "next": "15.0.3", "paytmchecksum": "^1.5.1", "postcss-cli": "^10.1.0", "react": "^18", "react-countup": "^6.1.1", "react-dom": "^18", "react-icons": "^5.0.1", "react-intersection-observer": "^9.5.3", "react-router-dom": "^6.0.2", "react-scripts": "^5.0.1", "react-scroll": "^1.8.4", "react-vertical-timeline-component": "^3.5.3", "request": "^2.88.2", "sqlite3": "^5.1.7", "styled-components": "^5.3.3", "tailwind-merge": "^2.2.0", "three": "^0.160.0", "validator": "^13.15.0", "web-vitals": "^1.0.1", "yarn": "^1.22.22"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start | node auth/server.js", "build": "yarn run react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:css": "postcss src/css/tailwind.css -o src/css/main.css", "prepare": "react-scripts --openssl-legacy-provider start | node auth/server.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@tailwindcss/forms": "^0.5.2", "@testing-library/dom": "^8.20.0", "autoprefixer": "^10.0.1", "css-loader": "^7.1.2", "@sendgrid/mail": "8.1.5", "eslint-config-next": "14.0.4", "postcss": "^8", "style-loader": "^4.0.0", "stylus": "^0.64.0", "stylus-loader": "^8.1.1", "tailwindcss": "^3.3.0"}}