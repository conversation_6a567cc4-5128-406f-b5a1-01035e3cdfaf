@import url("https://fonts.googleapis.com/css2?family=Audiowide&display=swap");
body {
  /* width: 100vw;
  height: 100vh; */
  background: transparent;
  /* color: #fff; */
  /* overflow: hidden; */
}
.background {
  /* background: url("http://updates.eveonline.com/kvd74o0q2fjg/6LSX4DQNTGA2gkaAoUyyGm/dc6ba3c2210c2385b76ca444543167cf/SUMMER_-_FLEET_BOOSTS_REWORKb412.jpg?w=1920&fm=jpg&fl=progressive") center center no-repeat; */
  background-size: cover;
  position: absolute;
  font-size: 74px;
  top: 0;
  left: 0;
  /* opacity: 0; */
  width: 100%;
  height: 100%;
}
h2 {
  position: absolute;
  /* width: 100%; */
  font-family: "Audiowide";
  top: 30%;
  text-align: center;
  font-style: italic;
  font-size: 56px;
  text-transform: uppercase;
  /* letter-spacing: 30px; */
  transform: scale(1.2);
}
h2.zoom {
  transform: scale(1);
  transition: 10s ease-out;
}
h2 .underlay {
  position: absolute;
  width: 70%;
  height: 0;
  box-shadow: 0 0 5px 3px rgba(255,255,255,0.2), 0 0 5px 3px rgba(255,255,255,0.2) inset;
  /* left: 50%; */
  transform: translate3d(-50%, 17px, 0);
  opacity: 0;
  transition: 0.4s linear;
  border-radius: 50%;
}
h2 .underlay.show {
  width: 20%;
  opacity: 1;
  height: 14px;
  transform: translate3d(-50%, 10px, 0);
  transition: 0s linear;
}
h2 > span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: block;
}
h2 > span span {
  display: inline-block;
  background: #fff;
  margin-right: -38px;
  padding: 0 20px 0 35px;
  transition: 0.1s linear;
  box-shadow: 0 0 0vw 0vw rgba(80,90,255,0);
  text-shadow: 1px 1px 20px rgba(255,255,255,0.5);
}
h2 .initial span {
  opacity: 0;
}
h2 .initial.transInStart span:nth-child(2),
h2 .initial.transInStart span:nth-child(3) {
  opacity: 1;
  box-shadow: 0 0 15vw 15vw rgba(80,90,255,0.2);
  transition: 0s linear;
}
h2 .initial.transInMid span {
  opacity: 1;
  transition: 0s linear;
  box-shadow: 0 0 15vw 15vw rgba(80,90,255,0.2);
}
h2 .initial.transInMid span:nth-child(1) {
  transition-delay: 0.125s;
}
h2 .initial.transInMid span:nth-child(2) {
  transition-delay: 0.075s;
}
h2 .initial.transInMid span:nth-child(3) {
  transition-delay: 0s;
}
h2 .initial.transInFin span {
  opacity: 1;
  background: rgba(255,255,255,0);
  transition: 0.05s linear;
}
h2 .initial.transInFin span:nth-child(1) {
  transition-delay: 0.2s;
}
h2 .initial.transInFin span:nth-child(2) {
  transition-delay: 0.1s;
}
h2 .initial.transInFin span:nth-child(3) {
  transition-delay: 0s;
}
h2 .initial.transOutStart span {
  opacity: 0;
  transition: opacity 0.5s linear;
}
h2 .initial.transOutFin span {
  display: none;
}
h2 .final span {
  opacity: 0;
}
h2 .final span.show {
  box-shadow: 0 0 0vw 0vw !important;
  background: transparent !important;
  opacity: 1 !important;
}
h2 .final span.show:nth-child(2) {
  padding: 0 35px 0 20px;
}
h2 .final.transInStart span {
  opacity: 1;
  box-shadow: 0 0 15vw 15vw rgba(80,90,255,0.2);
  transition: 0s linear;
}
h2 .final.transInStepOne span:nth-child(1),
h2 .final.transInStepOne span:nth-child(2) {
  opacity: 1;
  box-shadow: 0 0 15vw 15vw rgba(80,90,255,0.2);
  transition: 0s linear;
}
h2 .final.transInStepTwo span {
  opacity: 0;
  box-shadow: 0 0 0vw 0vw;
  background: transparent;
  transition: 0s linear;
}
h2 .final.transInStepTwo span:nth-child(1) {
  transition-delay: 0.075s;
}
h2 .final.transInStepTwo span:nth-child(2) {
  transition-delay: 0s;
}
h2 .final.transInStepTwo span:nth-child(1) {
  opacity: 1;
}
h2 .final.transInStepThree span:nth-child(2) {
  opacity: 1;
  box-shadow: 0 0 15vw 15vw rgba(80,90,255,0.2);
  transition: 0s linear;
}
