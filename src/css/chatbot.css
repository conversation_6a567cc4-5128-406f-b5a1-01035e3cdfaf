body {
    margin: 0;
    padding: 0;
    background: #000000;
  }
  
  ul {
    margin: 0;
    padding: 0;
    display: flex;
    position: absolute;
    bottom: -2%;
    /* left: 50%; */
    right: -2%;
    transform: translate(-50%, -50%);
z-index: 1001;
  }
  
  ul li {
    list-style: none;
    margin: 0 15px;
  }
  
  ul li a {
    position: relative; 
    display: block;
    width: 60px;
    height: 60px;
    text-align: center;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    line-height: 63px;
    background: #071a3d;
    border-radius: 50%;
    font-size: 30px;
    color: #666;
    transition: .5s;
  }
  
  ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #ffee10;
    transition: .5s;
    transform: scale(.9);
    z-index: -1;
  }
  
  ul li a:hover::before {
    transform: scale(1.1);
    box-shadow: 0 0 15px #ffee10;
  }
  
  ul li a:hover {
    color: #ffee10;
    box-shadow: 0 0 5px #ffee10;
    text-shadow: 0 0 5px #ffee10;
  }