/*
! tailwindcss v3.0.24 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

@tailwind base;
@tailwind components;
@tailwind utilities;


*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
*/

html {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */
  tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */

}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

/* h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
} */

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/*
Ensure the default browser behavior of the `hidden` attribute.
*/

[hidden] {
  display: none;
}

*, ::before, ::after {
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}
.container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
    padding-right: 2rem;
    padding-left: 2rem;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
    padding-right: 5rem;
    padding-left: 5rem;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
    padding-right: 6rem;
    padding-left: 6rem;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
    padding-right: 8rem;
    padding-left: 8rem;
  }
}
.fixed {
  position: fixed;
}
.relative {
  position: relative;
}
.inset-0 {
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.z-30 {
  z-index: 30;
}
.z-20 {
  z-index: 20;
}
.z-10 {
  z-index: 10;
}
.float-right {
  float: right;
}
.m-4 {
  margin: 1rem;
}
.m-0 {
  margin: 0px;
}
.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mt-14 {
  margin-top: 3.5rem;
}
.mr-10 {
  margin-right: 2.5rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-0 {
  margin-left: 0px;
}
.ml-8 {
  margin-left: 2rem;
}
.block {
  display: block;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.h-full {
  height: 100%;
}
.h-5 {
  height: 1.25rem;
}
.h-7 {
  height: 1.75rem;
}
.max-h-screen {
  max-height: 100vh;
}
.w-full {
  width: 100%;
}
.w-64 {
  width: 16rem;
}
.w-96 {
  width: 24rem;
}
.w-40 {
  width: 10rem;
}
.w-5 {
  width: 1.25rem;
}
.w-36 {
  width: 9rem;
}
.w-7 {
  width: 1.75rem;
}
.w-24 {
  width: 6rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-screen-lg {
  max-width: 1024px;
}
.cursor-pointer {
  cursor: pointer;
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-0 {
  gap: 0px;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-none {
  border-style: none;
}
.border-ternary-light {
  --tw-border-opacity: 1;
  border-color: rgb(246 247 248  var(--tw-border-opacity));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219  var(--tw-border-opacity));
}
.border-primary-light {
  --tw-border-opacity: 1;
  border-color: rgb(247 248 252  var(--tw-border-opacity));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235  var(--tw-border-opacity));
}
.border-indigo-200 {
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254  var(--tw-border-opacity));
}
.border-opacity-50 {
  --tw-border-opacity: 0.5;
}
.bg-secondary-light {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255  var(--tw-bg-opacity));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0  var(--tw-bg-opacity));
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241  var(--tw-bg-opacity));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99  var(--tw-bg-opacity));
}
.bg-primary-light {
  --tw-bg-opacity: 1;
  background-color: rgb(247 248 252  var(--tw-bg-opacity));
}
.bg-ternary-light {
  --tw-bg-opacity: 1;
  background-color: rgb(246 247 248  var(--tw-bg-opacity));
}
.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255  var(--tw-bg-opacity));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251  var(--tw-bg-opacity));
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.fill-current {
  fill: currentColor;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-4 {
  padding: 1rem;
}
.p-3 {
  padding: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pt-10 {
  padding-top: 2.5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.leading-loose {
  line-height: 2;
}
.leading-none {
  line-height: 1;
}
.leading-normal {
  line-height: 1.5;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-primary-dark {
  --tw-text-opacity: 1;
  color: rgb(13 36 56  var(--tw-text-opacity));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255  var(--tw-text-opacity));
}
.text-primary-light {
  --tw-text-opacity: 1;
  color: rgb(247 248 252  var(--tw-text-opacity));
}
.text-secondary-dark {
  --tw-text-opacity: 1;
  color: rgb(16 45 68  var(--tw-text-opacity));
}
.text-ternary-dark {
  --tw-text-opacity: 1;
  color: rgb(30 56 81  var(--tw-text-opacity));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128  var(--tw-text-opacity));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175  var(--tw-text-opacity));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235  var(--tw-text-opacity));
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0  0.1), 0 4px 6px -4px rgb(0 0 0  0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0  0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0  0.1), 0 8px 10px -6px rgb(0 0 0  0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-100 {
  transition-duration: 100ms;
}
.hover\:bg-indigo-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 var(--tw-bg-opacity));
}
.hover\:bg-ternary-dark:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 56 81 var(--tw-bg-opacity));
}
.hover\:bg-indigo-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 var(--tw-bg-opacity));
}
.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 var(--tw-bg-opacity));
}
.hover\:text-indigo-500:hover {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 var(--tw-text-opacity));
}
.hover\:text-primary-dark:hover {
  --tw-text-opacity: 1;
  color: rgb(13 36 56 var(--tw-text-opacity));
}
.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 var(--tw-text-opacity));
}
.hover\:text-indigo-600:hover {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 var(--tw-text-opacity));
}
.hover\:text-gray-400:hover {
  --tw-text-opacity: 1;
  color: rgb(156 163 175  var(--tw-text-opacity));
}
.hover\:text-gray-50:hover {
  --tw-text-opacity: 1;
  color: rgb(249 250 251  var(--tw-text-opacity));
}
.hover\:text-secondary-dark:hover {
  --tw-text-opacity: 1;
  color: rgb(16 45 68  var(--tw-text-opacity));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0  0.1), 0 8px 10px -6px rgb(0 0 0  0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-indigo-900:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(49 46 129  var(--tw-ring-opacity));
}
.dark .dark\:border-ternary-dark {
  --tw-border-opacity: 1;
  border-color: rgb(30 56 81  var(--tw-border-opacity));
}
.dark .dark\:border-secondary-dark {
  --tw-border-opacity: 1;
  border-color: rgb(16 45 68  var(--tw-border-opacity));
}
.dark .dark\:border-primary-dark {
  --tw-border-opacity: 1;
  border-color: rgb(13 36 56  var(--tw-border-opacity));
}
.dark .dark\:bg-primary-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(13 36 56  var(--tw-bg-opacity));
}
.dark .dark\:bg-ternary-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(30 56 81  var(--tw-bg-opacity));
}
.dark .dark\:bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235  var(--tw-bg-opacity));
}
.dark .dark\:bg-secondary-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(16 45 68  var(--tw-bg-opacity));
}
.dark .dark\:font-medium {
  font-weight: 500;
}
.dark .dark\:text-primary-light {
  --tw-text-opacity: 1;
  color: rgb(247 248 252  var(--tw-text-opacity));
}
.dark .dark\:text-ternary-light {
  --tw-text-opacity: 1;
  color: rgb(246 247 248  var(--tw-text-opacity));
}
.dark .dark\:text-secondary-dark {
  --tw-text-opacity: 1;
  color: rgb(16 45 68  var(--tw-text-opacity));
}
.dark .dark\:text-secondary-light {
  --tw-text-opacity: 1;
  color: rgb(255 255 255  var(--tw-text-opacity));
}
.dark .dark\:text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175  var(--tw-text-opacity));
}
.dark .dark\:text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235  var(--tw-text-opacity));
}
.dark .dark\:hover\:bg-primary-light:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(247 248 252  var(--tw-bg-opacity));
}
.dark .dark\:hover\:text-indigo-400:hover {
  --tw-text-opacity: 1;
  color: rgb(129 140 248  var(--tw-text-opacity));
}
.dark .dark\:hover\:text-primary-light:hover {
  --tw-text-opacity: 1;
  color: rgb(247 248 252  var(--tw-text-opacity));
}
.dark .dark\:hover\:text-indigo-300:hover {
  --tw-text-opacity: 1;
  color: rgb(165 180 252  var(--tw-text-opacity));
}
.dark .dark\:hover\:text-secondary-light:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255  var(--tw-text-opacity));
}
@media (min-width: 640px) {

  .sm\:container {
    width: 100%;
    padding-right: 1rem;
    padding-left: 1rem;
  }

  @media (min-width: 640px) {

    .sm\:container {
      max-width: 640px;
      padding-right: 2rem;
      padding-left: 2rem;
    }
  }

  @media (min-width: 768px) {

    .sm\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {

    .sm\:container {
      max-width: 1024px;
      padding-right: 5rem;
      padding-left: 5rem;
    }
  }

  @media (min-width: 1280px) {

    .sm\:container {
      max-width: 1280px;
      padding-right: 6rem;
      padding-left: 6rem;
    }
  }

  @media (min-width: 1536px) {

    .sm\:container {
      max-width: 1536px;
      padding-right: 8rem;
      padding-left: 8rem;
    }
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-10 {
    margin-top: 2.5rem;
  }

  .sm\:mt-20 {
    margin-top: 5rem;
  }

  .sm\:mt-14 {
    margin-top: 3.5rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-14 {
    margin-bottom: 3.5rem;
  }

  .sm\:mt-16 {
    margin-top: 4rem;
  }

  .sm\:mr-3 {
    margin-right: 0.75rem;
  }

  .sm\:mb-28 {
    margin-bottom: 7rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:mt-3 {
    margin-top: 0.75rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-6 {
    height: 1.5rem;
  }

  .sm\:w-1\/4 {
    width: 25%;
  }

  .sm\:w-3\/4 {
    width: 75%;
  }

  .sm\:w-1\/3 {
    width: 33.333333%;
  }

  .sm\:w-2\/3 {
    width: 66.666667%;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-10 {
    gap: 2.5rem;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:border-t-0 {
    border-top-width: 0px;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .sm\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .sm\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:pb-1 {
    padding-bottom: 0.25rem;
  }

  .sm\:pt-14 {
    padding-top: 3.5rem;
  }

  .sm\:pt-2 {
    padding-top: 0.5rem;
  }

  .sm\:pt-0 {
    padding-top: 0px;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 768px) {

  .md\:mt-2 {
    margin-top: 0.5rem;
  }

  .md\:flex {
    display: flex;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:max-w-xl {
    max-width: 36rem;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
@media (min-width: 1024px) {

  .lg\:mt-10 {
    margin-top: 2.5rem;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:max-w-xl {
    max-width: 36rem;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
@media (min-width: 1280px) {

  .xl\:max-w-xl {
    max-width: 36rem;
  }

  .xl\:max-w-screen-xl {
    max-width: 1280px;
  }

  .xl\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .xl\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}


