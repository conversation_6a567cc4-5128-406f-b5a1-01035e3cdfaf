/* General styles */
body {
  width: 100vw;
  height: 100vh;
  background: #101010;
  color: #fff;
  overflow: hidden;
}

.background {
  background-color: black;
  /* background: url("http://updates.eveonline.com/kvd74o0q2fjg/6LSX4DQNTGA2gkaAoUyyGm/dc6ba3c2210c2385b76ca444543167cf/SUMMER_-_FLEET_BOOSTS_REWORKb412.jpg?w=1920&fm=jpg&fl=progressive")
    center center no-repeat; */
  background-size: cover;
  font-size: 56px;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 1.0;
  width: 100%;
  height: 100%;
}

h2 {
  position: absolute;
  width: 100%;
  top: 30%;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 30px;
  transform: scale(1.2);
}

h2.zoom {
  transform: scale(1);
  transition: 10s ease-out;
}

/* Underlay styles */
.underlay {
  position: absolute;
  width: 70%;
  height: 0;
  box-shadow: 0 0 5px 3px rgb(252, 252, 252), 0 0 5px 3px rgb(253, 253, 253) inset;
  left: 50%;
  transform: translate3d(-50%, 17px, 0);
  opacity: 0;
  transition: 0.2s linear;
  border-radius: 50%;
}

.underlay.show {
  width: 20%;
  opacity: 1;
  height: 14px;
  transform: translate3d(-50%, 10px, 0);
  transition: 0s linear;
}

/* Initial state for .initial and .final spans */
.initial span,
.final span {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

/* Transition for .initial spans */
.initial.transInStart span {
  opacity: 1;
  transform: translateY(0);
}

.initial.transInMid span {
  opacity: 1;
  transform: translateY(0);
}

.initial.transInFin span {
  opacity: 1;
  transform: translateY(0);
}

.initial.transOutStart span {
  opacity: 0;
  transform: translateY(-20px);
}

.initial.transOutFin span {
  display: none;
}

/* Transition for .final spans */
.final span {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

.final.transInStart span {
  opacity: 1;
  transform: scale(1);
}

.final.transInStepOne span:nth-child(1),
.final.transInStepOne span:nth-child(2) {
  opacity: 1;
  transform: scale(1);
}

.final.transInStepTwo span {
  opacity: 0;
  transform: scale(0.8);
}

.final.transInStepThree span:nth-child(2) {
  opacity: 1;
  transform: scale(1.1);
}

/* Debugging styles (optional) */
.initial span {
  background-color: rgba(255, 0, 0, 0.5); /* Red for debugging */
}

.final span {
  background-color: rgba(0, 255, 0, 0.5); /* Green for debugging */
}

.underlay {
  background-color: rgba(0, 0, 255, 0.5); /* Blue for debugging */
}